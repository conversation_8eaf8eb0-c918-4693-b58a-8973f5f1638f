'use client';

import { useRef } from 'react';
import { CursorImageEffect } from '@/components/ui/aceternity/cursor-image-effect';
import { useLanguage } from '@/lib/context/language-context';
import { motion, useScroll, useTransform } from 'framer-motion';
import TextHoverEffectDemo from '@/components/ui/text-hover-effect-demo';
import { BokehBackground, GlassCard } from '@/components/ui/aceternity/bokeh-background';
import { TextGenerateEffect } from '@/components/ui/text-generate-effect';
import { SmoothReveal } from '@/components/ui/smooth-reveal';
import { AnimatedCard } from '@/components/ui/animated-card';
import { HoverBorderGradient } from '@/components/ui/hover-border-gradient';

import {
  RocketIcon,
  HeartIcon,
  GlobeIcon,
  TargetIcon,
  ArrowRightIcon
} from '@radix-ui/react-icons';

export default function AboutPage() {
  const { t } = useLanguage();
  const containerRef = useRef<HTMLDivElement>(null);

  // Scroll progress for parallax effects
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  // Parallax transforms
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100]);

  // Investor images for cursor effect
  const investorImages = [
    '/images/investors/1.png',
    '/images/investors/2.png',
    '/images/investors/3.png',
    '/images/investors/4.png',
    '/images/investors/5.png',
  ];

  // Enhanced team members data with detailed information
  const teamMembers = [
    {
      id: 1,
      name: t('about.teamMember1'),
      role: t('about.teamMember1Role'),
      image: '/images/Team/team1.jpg',
      bio: 'Visionary leader with 10+ years of experience in business development and entrepreneurship. Passionate about fostering innovation and supporting the next generation of entrepreneurs.',
      achievements: ['Founded 3 successful startups', 'Raised $50M+ in funding', 'Mentor to 100+ entrepreneurs'],
      specialties: ['Strategic Planning', 'Business Development', 'Fundraising', 'Leadership'],
      quote: "Innovation is not just about technology; it's about creating solutions that matter.",
      social: {
        linkedin: 'https://linkedin.com/in/khandmaa',
        twitter: 'https://twitter.com/khandmaa',
        email: '<EMAIL>'
      },
      gradient: 'from-purple-500 via-purple-600 to-indigo-600',
      accentColor: '#8b5cf6'
    },
    {
      id: 2,
      name: t('about.teamMember2'),
      role: t('about.teamMember2Role'),
      image: '/images/Team/team2.jpg',
      bio: 'Strategic operations expert with extensive experience in program management and startup acceleration. Dedicated to creating efficient systems that help entrepreneurs succeed.',
      achievements: ['Managed 50+ startup programs', 'Improved success rates by 40%', 'Built scalable processes'],
      specialties: ['Program Management', 'Startup Acceleration', 'Innovation Strategy', 'Mentorship'],
      quote: "Every great company started as an idea. We help turn those ideas into reality.",
      social: {
        linkedin: 'https://linkedin.com/in/zolboot',
        twitter: 'https://twitter.com/zolboot',
        email: '<EMAIL>'
      },
      gradient: 'from-cyan-500 via-blue-600 to-indigo-600',
      accentColor: '#06b6d4'
    },
    {
      id: 3,
      name: t('about.teamMember3'),
      role: t('about.teamMember3Role'),
      image: '/images/Team/team3.jpg',
      bio: 'Marketing and operations specialist focused on building strong relationships with entrepreneurs and creating effective communication strategies.',
      achievements: ['Managed 30+ marketing campaigns', 'Built community of 1000+ entrepreneurs', 'Expert in digital marketing'],
      specialties: ['Marketing Strategy', 'Operations Management', 'Community Building', 'Digital Communications'],
      quote: "Building connections and fostering growth through strategic marketing and operations.",
      social: {
        linkedin: 'https://linkedin.com/in/ankhtsetseg',
        twitter: 'https://twitter.com/ankhtsetseg',
        email: '<EMAIL>'
      },
      gradient: 'from-emerald-500 via-green-600 to-teal-600',
      accentColor: '#10b981'
    },
    {
      id: 4,
      name: t('about.teamMember4'),
      role: t('about.teamMember4Role'),
      image: '/images/Team/team4.jpg',
      bio: 'Experienced project manager with a track record of successfully delivering complex startup programs and initiatives.',
      achievements: ['Delivered 40+ projects on time', 'Managed cross-functional teams', 'Process optimization expert'],
      specialties: ['Project Management', 'Team Leadership', 'Process Optimization', 'Strategic Planning'],
      quote: "Success comes from meticulous planning and flawless execution.",
      social: {
        linkedin: 'https://linkedin.com/in/munkhbileg',
        twitter: 'https://twitter.com/munkhbileg',
        email: '<EMAIL>'
      },
      gradient: 'from-amber-500 via-orange-600 to-red-600',
      accentColor: '#f59e0b'
    },
    {
      id: 5,
      name: t('about.teamMember5'),
      role: t('about.teamMember5Role'),
      image: '/images/Team/team5.jpg',
      bio: 'Dedicated project manager specializing in startup acceleration programs and innovation initiatives.',
      achievements: ['Led 25+ acceleration programs', 'Mentored 100+ startups', 'Innovation program specialist'],
      specialties: ['Project Management', 'Startup Acceleration', 'Innovation Programs', 'Mentorship'],
      quote: "Every project is an opportunity to create lasting impact.",
      social: {
        linkedin: 'https://linkedin.com/in/dolgoon',
        twitter: 'https://twitter.com/dolgoon',
        email: '<EMAIL>'
      },
      gradient: 'from-rose-500 via-pink-600 to-purple-600',
      accentColor: '#ec4899'
    },
  ];

  // Enhanced mentors data with detailed information
  const mentors = [
    {
      id: 1,
      name: t('about.mentor1'),
      role: t('about.mentor1Role'),
      image: '/images/Team/team5.jpg',
      bio: 'Seasoned financial strategist with 20+ years of experience in investment banking and venture capital. Expert in helping startups navigate complex financial landscapes.',
      achievements: ['$500M+ in deals closed', 'Former Goldman Sachs VP', '100+ startups funded'],
      specialties: ['Financial Strategy', 'Investment Banking', 'Venture Capital', 'Risk Management'],
      quote: "Smart capital allocation is the foundation of sustainable business growth.",
      experience: '20+ Years',
      companiesMentored: '150+',
      successRate: '85%',
      social: {
        linkedin: 'https://linkedin.com/in/byambakhorloo',
        twitter: 'https://twitter.com/byambakhorloo',
        email: '<EMAIL>'
      },
      gradient: 'from-red-500 via-pink-600 to-rose-600',
      accentColor: '#ef4444'
    },
    {
      id: 2,
      name: t('about.mentor2'),
      role: t('about.mentor2Role'),
      image: '/images/Team/team6.jpg',
      bio: 'Operations excellence expert with proven track record in scaling businesses from startup to IPO. Specializes in operational efficiency and growth strategies.',
      achievements: ['Scaled 5 companies to $100M+', 'Former McKinsey Partner', 'Operations expert'],
      specialties: ['Operations Strategy', 'Business Scaling', 'Process Optimization', 'Growth Management'],
      quote: "Operational excellence is the invisible force behind every successful company.",
      experience: '18+ Years',
      companiesMentored: '200+',
      successRate: '92%',
      social: {
        linkedin: 'https://linkedin.com/in/todrol',
        twitter: 'https://twitter.com/todrol',
        email: '<EMAIL>'
      },
      gradient: 'from-purple-500 via-violet-600 to-indigo-600',
      accentColor: '#8b5cf6'
    },
    {
      id: 3,
      name: 'Dr. N. Batbold',
      role: 'Innovation & Technology Advisor',
      image: '/images/Team/team7.jpg',
      bio: 'Technology visionary and innovation expert with PhD in Computer Science. Leading authority on emerging technologies and digital transformation strategies.',
      achievements: ['PhD in Computer Science', '50+ patents filed', 'Former Google Research'],
      specialties: ['Innovation Strategy', 'Emerging Technologies', 'Digital Transformation', 'R&D Management'],
      quote: "Innovation happens at the intersection of technology and human needs.",
      experience: '15+ Years',
      companiesMentored: '80+',
      successRate: '88%',
      social: {
        linkedin: 'https://linkedin.com/in/batbold',
        twitter: 'https://twitter.com/batbold',
        email: '<EMAIL>'
      },
      gradient: 'from-cyan-500 via-blue-600 to-indigo-600',
      accentColor: '#06b6d4'
    },
    {
      id: 4,
      name: 'M. Enkhjin',
      role: 'Investment & Growth Strategist',
      image: '/images/Team/team3.jpg',
      bio: 'Investment strategist with deep expertise in growth equity and strategic partnerships. Helps startups secure funding and build strategic relationships.',
      achievements: ['$1B+ in funding facilitated', 'Former Sequoia Capital', 'Growth strategy expert'],
      specialties: ['Investment Strategy', 'Growth Equity', 'Strategic Partnerships', 'Market Expansion'],
      quote: "The right investment strategy can turn potential into exponential growth.",
      experience: '12+ Years',
      companiesMentored: '120+',
      successRate: '90%',
      social: {
        linkedin: 'https://linkedin.com/in/enkhjin',
        twitter: 'https://twitter.com/enkhjin',
        email: '<EMAIL>'
      },
      gradient: 'from-emerald-500 via-green-600 to-teal-600',
      accentColor: '#10b981'
    },
  ];

  // Vision, Mission, Values
  const visionMissionValues = [
    {
      icon: <RocketIcon className="w-8 h-8" />,
      title: t('about.ourVision'),
      description: t('about.visionText'),
    },
    {
      icon: <TargetIcon className="w-8 h-8" />,
      title: t('about.ourMission'),
      description: t('about.missionText'),
    },
    {
      icon: <HeartIcon className="w-8 h-8" />,
      title: t('about.ourValues'),
      description: t('about.valuesText'),
    },
  ];

  // Enhanced Statistics with more details
  const stats = [
    {
      number: '200+',
      label: 'Startups Supported',
      description: 'Innovative companies launched',
      icon: '🚀',
      color: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-500/10 to-cyan-500/10'
    },
    {
      number: '50M+',
      label: 'Funding Raised',
      description: 'Total investment secured',
      icon: '💰',
      color: 'from-green-500 to-emerald-500',
      bgGradient: 'from-green-500/10 to-emerald-500/10'
    },
    {
      number: '95%',
      label: 'Success Rate',
      description: 'Companies still operating',
      icon: '📈',
      color: 'from-purple-500 to-pink-500',
      bgGradient: 'from-purple-500/10 to-pink-500/10'
    },
    {
      number: '15+',
      label: 'Industry Experts',
      description: 'Experienced mentors',
      icon: '👥',
      color: 'from-orange-500 to-red-500',
      bgGradient: 'from-orange-500/10 to-red-500/10'
    },
  ];

  return (
    <div ref={containerRef} className="relative">
      {/* Background Text Effect - Footer Area Only */}
      <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[-10] flex items-center justify-center pointer-events-none">
        <div className="pointer-events-auto">
          <TextHoverEffectDemo />
        </div>
      </div>

      {/* Hero Section with Modern Design */}
      <BokehBackground
        className="min-h-screen relative overflow-hidden"
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9', '#fbbf24', '#f59e0b']}
        density={80}
        speed={1.2}
      >
        <div className="container mx-auto px-4 min-h-screen flex items-center justify-center">
          <div className="text-center max-w-5xl mx-auto">
            <SmoothReveal direction="up" delay={0.2}>
              <motion.div
                className="inline-block rounded-full bg-primary/10 px-6 py-3 text-sm font-medium text-primary mb-8 border border-primary/20"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
{t('nav.about')}
              </motion.div>
            </SmoothReveal>

            <SmoothReveal direction="up" delay={0.4}>
              <h1 className="text-5xl md:text-7xl font-bold mb-8 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent leading-tight">
                {t('hero.title')}
              </h1>
            </SmoothReveal>

            <SmoothReveal direction="up" delay={0.6}>
              <div className="max-w-3xl mx-auto mb-12">
                <TextGenerateEffect
                  words={t('hero.subtitle')}
                  className="text-xl md:text-2xl text-white/80 leading-relaxed"
                  duration={0.8}
                />
              </div>
            </SmoothReveal>

            <SmoothReveal direction="up" delay={0.8}>
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <HoverBorderGradient
                  containerClassName="rounded-full"
                  className="bg-black text-white px-8 py-4 text-lg font-medium"
                >
                  <span className="flex items-center gap-2">
                    Explore Our Programs
                    <ArrowRightIcon className="w-5 h-5" />
                  </span>
                </HoverBorderGradient>

                <motion.button
                  className="px-8 py-4 text-lg font-medium text-white/80 hover:text-white transition-colors duration-300 border border-white/20 rounded-full hover:border-white/40"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Meet Our Team
                </motion.button>
              </div>
            </SmoothReveal>
          </div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <motion.div
              className="w-1 h-3 bg-white/60 rounded-full mt-2"
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>
        </motion.div>
      </BokehBackground>

      {/* Keep the original cursor effect section unchanged */}
      <CursorImageEffect
        className="min-h-[90vh] flex flex-col items-center justify-center pt-32 pb-20 bg-black relative"
        images={investorImages}
        imageSize={300}
        imageOpacity={0.8}
      >
        <div className="hidden">Cursor effect content</div>
      </CursorImageEffect>

      {/* Company Values Section */}
      <section className="py-32 bg-gradient-to-b from-black via-gray-900/50 to-black relative overflow-hidden">
        <div className="container mx-auto px-4">
          <SmoothReveal direction="up" className="text-center mb-20">
            <motion.div
              className="inline-block rounded-full bg-primary/10 px-6 py-3 text-sm font-medium text-primary mb-8 border border-primary/20"
              whileHover={{ scale: 1.05 }}
            >
              Our Foundation
            </motion.div>
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
              Vision, Mission & Values
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
              The core principles that guide our work in fostering innovation and supporting entrepreneurs.
            </p>
          </SmoothReveal>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {visionMissionValues.map((value, index) => (
              <SmoothReveal key={index} direction="up" delay={index * 0.1}>
                <AnimatedCard
                  variant="glass"
                  hoverEffect="scale"
                  className="p-8 h-full text-center group"
                  delay={index * 0.1}
                >
                  <motion.div
                    className="text-primary mb-6 flex justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.3 }}
                  >
                    {value.icon}
                  </motion.div>
                  <h3 className="text-xl font-bold text-white mb-4 group-hover:text-primary transition-colors duration-300">
                    {value.title}
                  </h3>
                  <p className="text-white/70 leading-relaxed">
                    {value.description}
                  </p>
                </AnimatedCard>
              </SmoothReveal>
            ))}
          </div>
        </div>
      </section>

      {/* What We Do Section - Premium Enhanced */}
      <BokehBackground
        className="py-32 relative overflow-hidden"
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9', '#fbbf24', '#f59e0b']}
        density={80}
        speed={1.2}
      >
        {/* Additional floating elements for depth */}
        <div className="absolute inset-0 pointer-events-none">
          <motion.div
            className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl"
            animate={{
              x: [0, 50, -30, 0],
              y: [0, -30, 40, 0],
              scale: [1, 1.2, 0.8, 1],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-purple-500/10 rounded-full blur-2xl"
            animate={{
              x: [0, -40, 60, 0],
              y: [0, 50, -20, 0],
              scale: [1, 0.7, 1.3, 1],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative">
          <SmoothReveal direction="up" className="text-center mb-24">
            <motion.div
              className="relative inline-block mb-8"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-purple-500/20 blur-xl"
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <div className="relative rounded-full bg-primary/10 px-8 py-4 text-sm font-medium text-primary border border-primary/30 backdrop-blur-md">
                <span className="flex items-center gap-2">
                  <motion.div
                    className="w-2 h-2 bg-primary rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  Our Mission & Vision
                </span>
              </div>
            </motion.div>

            <motion.h2
              className="text-5xl md:text-7xl font-bold mb-8 leading-tight"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <span className="bg-gradient-to-r from-white via-white to-white/90 bg-clip-text text-transparent">
                {t('about.whatWeDoTitle')}
              </span>
              <br />
              <motion.span
                className="bg-gradient-to-r from-primary via-purple-400 to-pink-400 bg-clip-text text-transparent"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
              >
                Excellence
              </motion.span>
            </motion.h2>

            <motion.div
              className="max-w-5xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <TextGenerateEffect
                words="We empower entrepreneurs and innovators through comprehensive support, cutting-edge resources, and strategic guidance that transforms visionary ideas into thriving, successful businesses that shape the future."
                className="text-xl md:text-2xl text-white/80 leading-relaxed"
                duration={1.2}
              />
            </motion.div>

            {/* Mission highlights */}
            <motion.div
              className="flex flex-wrap justify-center gap-6 mt-12"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
            >
              {[
                { icon: "🚀", text: "Innovation" },
                { icon: "💡", text: "Guidance" },
                { icon: "🌟", text: "Excellence" },
                { icon: "🤝", text: "Partnership" }
              ].map((item, index) => (
                <motion.div
                  key={item.text}
                  className="flex items-center gap-3 px-6 py-3 rounded-full bg-white/5 backdrop-blur-md border border-white/10"
                  whileHover={{ scale: 1.05, backgroundColor: "rgba(255,255,255,0.1)" }}
                  transition={{ duration: 0.3 }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  style={{ transitionDelay: `${index * 0.1}s` }}
                >
                  <span className="text-2xl">{item.icon}</span>
                  <span className="text-white/80 font-medium">{item.text}</span>
                </motion.div>
              ))}
            </motion.div>
          </SmoothReveal>

          {/* Enhanced Cards Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {[
              {
                content: t('about.whatWeDoItem1'),
                icon: "🚀",
                title: "Accelerator Program",
                gradient: "from-blue-500/20 to-purple-500/20"
              },
              {
                content: t('about.whatWeDoItem2'),
                icon: "💡",
                title: "Innovation Marathon",
                gradient: "from-purple-500/20 to-pink-500/20"
              },
              {
                content: t('about.whatWeDoItem3'),
                icon: "🎯",
                title: "Future Program",
                gradient: "from-pink-500/20 to-red-500/20"
              },
              {
                content: t('about.whatWeDoItem4'),
                icon: "🎙️",
                title: "InnoHub Podcast",
                gradient: "from-green-500/20 to-blue-500/20"
              },
              {
                content: t('about.whatWeDoItem5'),
                icon: "🤝",
                title: "Investors Network",
                gradient: "from-yellow-500/20 to-orange-500/20"
              },
            ].map((item, index) => (
              <SmoothReveal key={index} direction="up" delay={index * 0.15}>
                <motion.div
                  className="relative group"
                  whileHover={{ y: -8 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                >
                  {/* Background glow effect */}
                  <motion.div
                    className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${item.gradient} blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                    initial={false}
                  />

                  <GlassCard className="relative p-8 h-full group-hover:bg-white/10 transition-all duration-500 border-white/10 group-hover:border-primary/30">
                    {/* Header with icon and number */}
                    <div className="flex items-center justify-between mb-6">
                      <motion.div
                        className="flex items-center gap-4"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <motion.div
                          className="text-3xl"
                          whileHover={{ scale: 1.2, rotate: 10 }}
                          transition={{ duration: 0.3 }}
                        >
                          {item.icon}
                        </motion.div>
                        <div>
                          <h3 className="text-xl font-bold text-white group-hover:text-primary transition-colors duration-300">
                            {item.title}
                          </h3>
                          <div className="w-12 h-0.5 bg-gradient-to-r from-primary to-purple-400 mt-2 group-hover:w-20 transition-all duration-300" />
                        </div>
                      </motion.div>

                      <motion.div
                        className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold text-lg border border-primary/30 group-hover:bg-primary/30 group-hover:scale-110 transition-all duration-300"
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                      >
                        {(index + 1).toString().padStart(2, '0')}
                      </motion.div>
                    </div>

                    {/* Content */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 + 0.2 }}
                      viewport={{ once: true }}
                    >
                      <p className="text-white/90 leading-relaxed text-lg group-hover:text-white transition-colors duration-300">
                        {item.content}
                      </p>
                    </motion.div>

                    {/* Bottom accent line */}
                    <motion.div
                      className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-primary to-purple-400 rounded-b-2xl w-0 group-hover:w-full transition-all duration-500"
                      initial={false}
                    />

                    {/* Floating particles effect */}
                    <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
                      {[...Array(3)].map((_, particleIndex) => (
                        <motion.div
                          key={particleIndex}
                          className="absolute w-1 h-1 bg-primary/40 rounded-full"
                          style={{
                            left: `${20 + particleIndex * 30}%`,
                            top: `${30 + particleIndex * 20}%`,
                          }}
                          animate={{
                            y: [-10, -20, -10],
                            opacity: [0, 1, 0],
                            scale: [0.5, 1, 0.5],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            delay: particleIndex * 0.5,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </div>
                  </GlassCard>
                </motion.div>
              </SmoothReveal>
            ))}
          </div>
        </div>
      </BokehBackground>

      {/* Our Impact Section - Modern Enhanced */}
      <section className="py-32 bg-gradient-to-b from-black via-gray-900/20 to-black relative overflow-hidden">
        {/* Dynamic background elements */}
        <div className="absolute inset-0 pointer-events-none">
          <motion.div
            className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-purple-500/10 rounded-full blur-3xl"
            animate={{
              x: [0, 100, -50, 0],
              y: [0, -50, 100, 0],
              scale: [1, 1.2, 0.8, 1],
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-1/3 right-1/4 w-64 h-64 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-full blur-2xl"
            animate={{
              x: [0, -80, 60, 0],
              y: [0, 80, -40, 0],
              scale: [1, 0.7, 1.3, 1],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative">
          <SmoothReveal direction="up" className="text-center mb-24">
            <motion.div
              className="relative inline-block mb-8"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-blue-500/20 blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <div className="relative rounded-full bg-primary/10 px-8 py-4 text-sm font-medium text-primary border border-primary/30 backdrop-blur-md">
                <span className="flex items-center gap-2">
                  <motion.div
                    className="w-2 h-2 bg-primary rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  Our Impact & Achievements
                </span>
              </div>
            </motion.div>

            <motion.h2
              className="text-5xl md:text-7xl font-bold mb-8 leading-tight"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <span className="bg-gradient-to-r from-white via-white to-white/90 bg-clip-text text-transparent">
                Numbers That
              </span>
              <br />
              <motion.span
                className="bg-gradient-to-r from-primary via-blue-400 to-cyan-400 bg-clip-text text-transparent"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
              >
                Define Success
              </motion.span>
            </motion.h2>

            <motion.div
              className="max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <TextGenerateEffect
                words="Our commitment to excellence is reflected in the measurable success of our community, the transformative impact we've created, and the thriving ecosystem we've built together."
                className="text-xl md:text-2xl text-white/80 leading-relaxed"
                duration={1.0}
              />
            </motion.div>
          </SmoothReveal>

          {/* Enhanced Statistics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {stats.map((stat, index) => (
              <SmoothReveal key={index} direction="up" delay={index * 0.15}>
                <motion.div
                  className="relative group"
                  whileHover={{ y: -12 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                >
                  {/* Background glow effect */}
                  <motion.div
                    className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${stat.bgGradient} blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                    initial={false}
                  />

                  <GlassCard className="relative p-8 h-full text-center group-hover:bg-white/10 transition-all duration-500 border-white/10 group-hover:border-primary/30">
                    {/* Icon with animation */}
                    <motion.div
                      className="text-5xl mb-6"
                      initial={{ opacity: 0, scale: 0.5 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.2, rotate: 10 }}
                    >
                      {stat.icon}
                    </motion.div>

                    {/* Animated number */}
                    <motion.div
                      className={`text-4xl md:text-5xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-4`}
                      initial={{ opacity: 0, scale: 0.3 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{
                        duration: 1.2,
                        delay: index * 0.2,
                        type: "spring",
                        stiffness: 100
                      }}
                      viewport={{ once: true }}
                    >
                      {stat.number}
                    </motion.div>

                    {/* Label and description */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 + 0.3 }}
                      viewport={{ once: true }}
                    >
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-primary transition-colors duration-300">
                        {stat.label}
                      </h3>
                      <p className="text-white/60 text-sm group-hover:text-white/80 transition-colors duration-300">
                        {stat.description}
                      </p>
                    </motion.div>

                    {/* Progress indicator */}
                    <motion.div
                      className={`absolute bottom-0 left-0 h-1 bg-gradient-to-r ${stat.color} rounded-b-3xl w-0 group-hover:w-full transition-all duration-700`}
                      initial={false}
                    />

                    {/* Floating particles */}
                    <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-3xl">
                      {[...Array(4)].map((_, particleIndex) => (
                        <motion.div
                          key={particleIndex}
                          className={`absolute w-1 h-1 bg-gradient-to-r ${stat.color} rounded-full opacity-60`}
                          style={{
                            left: `${15 + particleIndex * 20}%`,
                            top: `${20 + particleIndex * 15}%`,
                          }}
                          animate={{
                            y: [-15, -25, -15],
                            opacity: [0, 1, 0],
                            scale: [0.5, 1, 0.5],
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            delay: particleIndex * 0.8,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </div>
                  </GlassCard>
                </motion.div>
              </SmoothReveal>
            ))}
          </div>

          {/* Additional impact metrics */}
          <motion.div
            className="mt-20 text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-wrap justify-center gap-8 max-w-4xl mx-auto">
              {[
                { metric: "3 Years", label: "Average Growth Time" },
                { metric: "85%", label: "Retention Rate" },
                { metric: "12+", label: "Countries Reached" },
                { metric: "500+", label: "Jobs Created" }
              ].map((item, index) => (
                <motion.div
                  key={item.label}
                  className="text-center"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-2xl font-bold text-primary mb-1">{item.metric}</div>
                  <div className="text-white/60 text-sm">{item.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Our Team Section - Modern Enhanced */}
      <section className="relative py-32 overflow-hidden">
        {/* Dynamic background with flowing gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black">
          <motion.div
            className="absolute top-0 left-0 w-full h-full opacity-30"
            style={{
              background: 'radial-gradient(circle at 20% 50%, rgba(139, 92, 246, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.3) 0%, transparent 50%)',
            }}
            animate={{
              opacity: [0.3, 0.5, 0.3],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <SmoothReveal direction="up" className="text-center mb-24">
            <motion.div
              className="relative inline-block mb-8"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-blue-500/20 blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <div className="relative rounded-full bg-primary/10 px-8 py-4 text-sm font-medium text-primary border border-primary/30 backdrop-blur-md">
                <span className="flex items-center gap-2">
                  <motion.div
                    className="w-2 h-2 bg-primary rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  Meet Our Team
                </span>
              </div>
            </motion.div>

            <motion.h2
              className="text-5xl md:text-7xl font-bold mb-8 leading-tight"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <span className="bg-gradient-to-r from-white via-white to-white/90 bg-clip-text text-transparent">
                {t('about.ourTeamTitle')}
              </span>
            </motion.h2>

            <motion.div
              className="max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <TextGenerateEffect
                words="Our diverse team of experts brings together decades of experience in entrepreneurship, technology, and business development to guide your success."
                className="text-xl md:text-2xl text-white/80 leading-relaxed"
                duration={1.0}
              />
            </motion.div>
          </SmoothReveal>

          {/* Modern Team Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {teamMembers.map((member, index) => (
              <SmoothReveal key={member.id} direction="up" delay={index * 0.2}>
                <motion.div
                  className="group relative"
                  whileHover={{ y: -8 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                >
                  {/* Background glow effect */}
                  <motion.div
                    className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${member.gradient} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500`}
                    initial={false}
                  />

                  <div className="relative bg-black/40 backdrop-blur-md border border-white/10 rounded-3xl p-8 group-hover:border-white/20 transition-all duration-500">
                    {/* Header with image and basic info */}
                    <div className="flex items-start space-x-6 mb-6">
                      <motion.div
                        className="relative w-24 h-24 flex-shrink-0"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="w-full h-full rounded-2xl overflow-hidden relative">
                          <img
                            src={member.image}
                            alt={member.name}
                            className="w-full h-full object-cover filter grayscale group-hover:grayscale-0 transition-all duration-500"
                          />
                          <motion.div
                            className={`absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-current transition-colors duration-300`}
                            style={{ color: member.accentColor }}
                            initial={false}
                          />
                        </div>
                      </motion.div>

                      <div className="flex-1">
                        <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-primary transition-colors duration-300">
                          {member.name}
                        </h3>
                        <p className="text-primary/80 font-medium mb-3">{member.role}</p>
                        <p className="text-white/70 text-sm leading-relaxed">{member.bio}</p>
                      </div>
                    </div>

                    {/* Quote */}
                    <motion.div
                      className="mb-6 p-4 rounded-xl bg-white/5 border-l-4 border-primary/50"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 + 0.3 }}
                      viewport={{ once: true }}
                    >
                      <p className="text-white/80 italic text-sm">"{member.quote}"</p>
                    </motion.div>

                    {/* Specialties */}
                    <div className="mb-6">
                      <h4 className="text-white/90 font-medium mb-3 text-sm">Specialties</h4>
                      <div className="flex flex-wrap gap-2">
                        {member.specialties.map((specialty, skillIndex) => (
                          <motion.span
                            key={specialty}
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ delay: index * 0.1 + skillIndex * 0.05 }}
                            className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium transition-colors border-primary/30 bg-primary/10 text-primary hover:bg-primary/20"
                          >
                            {specialty}
                          </motion.span>
                        ))}
                      </div>
                    </div>

                    {/* Social links */}
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-3">
                        {Object.entries(member.social).map(([platform, url]) => (
                          <motion.a
                            key={platform}
                            href={url}
                            className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center text-white/60 hover:text-white hover:bg-primary/20 transition-all duration-300"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <span className="text-xs font-bold">{platform[0].toUpperCase()}</span>
                          </motion.a>
                        ))}
                      </div>

                      <motion.div
                        className={`w-3 h-3 rounded-full bg-gradient-to-r ${member.gradient}`}
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [0.7, 1, 0.7],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: index * 0.5,
                          ease: "easeInOut"
                        }}
                      />
                    </div>

                    {/* Bottom accent line */}
                    <motion.div
                      className={`absolute bottom-0 left-0 h-1 bg-gradient-to-r ${member.gradient} rounded-b-3xl w-0 group-hover:w-full transition-all duration-700`}
                      initial={false}
                    />
                  </div>
                </motion.div>
              </SmoothReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Mentors Section - Modern Enhanced */}
      <BokehBackground
        className="py-32 relative"
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9', '#fbbf24', '#f59e0b']}
        density={70}
        speed={1.3}
      >
        <div className="container mx-auto px-4 relative z-10">
          <SmoothReveal direction="up" className="text-center mb-24">
            <motion.div
              className="relative inline-block mb-8"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-orange-500/20 blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <div className="relative rounded-full bg-primary/10 px-8 py-4 text-sm font-medium text-primary border border-primary/30 backdrop-blur-md">
                <span className="flex items-center gap-2">
                  <motion.div
                    className="w-2 h-2 bg-primary rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  Expert Guidance & Mentorship
                </span>
              </div>
            </motion.div>

            <motion.h2
              className="text-5xl md:text-7xl font-bold mb-8 leading-tight"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <span className="bg-gradient-to-r from-white via-white to-white/90 bg-clip-text text-transparent">
                {t('about.mentorsTitle')}
              </span>
            </motion.h2>

            <motion.div
              className="max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <TextGenerateEffect
                words="Learn from industry veterans and successful entrepreneurs who provide personalized guidance, strategic insights, and proven methodologies to accelerate your entrepreneurial journey."
                className="text-xl md:text-2xl text-white/80 leading-relaxed"
                duration={1.0}
              />
            </motion.div>
          </SmoothReveal>

          {/* Modern Mentors Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {mentors.map((mentor, index) => (
              <SmoothReveal key={mentor.id} direction="up" delay={index * 0.2}>
                <motion.div
                  className="group relative"
                  whileHover={{ y: -12 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                >
                  {/* Enhanced background glow */}
                  <motion.div
                    className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${mentor.gradient} opacity-0 group-hover:opacity-30 blur-2xl transition-opacity duration-500`}
                    initial={false}
                  />

                  <div className="relative bg-black/50 backdrop-blur-md border border-white/20 rounded-3xl p-8 group-hover:border-white/30 transition-all duration-500">
                    {/* Header with enhanced layout */}
                    <div className="flex items-start space-x-6 mb-8">
                      <motion.div
                        className="relative w-28 h-28 flex-shrink-0"
                        whileHover={{ scale: 1.05, rotate: 2 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="w-full h-full rounded-2xl overflow-hidden relative">
                          <img
                            src={mentor.image}
                            alt={mentor.name}
                            className="w-full h-full object-cover filter grayscale group-hover:grayscale-0 transition-all duration-500"
                          />
                          <motion.div
                            className={`absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-current transition-colors duration-300`}
                            style={{ color: mentor.accentColor }}
                            initial={false}
                          />
                        </div>

                        {/* Experience badge */}
                        <motion.div
                          className="absolute -bottom-2 -right-2 bg-primary/90 text-white text-xs font-bold px-2 py-1 rounded-full border-2 border-white/20"
                          initial={{ opacity: 0, scale: 0.8 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.1 + 0.5 }}
                          viewport={{ once: true }}
                        >
                          {mentor.experience}
                        </motion.div>
                      </motion.div>

                      <div className="flex-1">
                        <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-primary transition-colors duration-300">
                          {mentor.name}
                        </h3>
                        <p className="text-primary/90 font-medium mb-4">{mentor.role}</p>
                        <p className="text-white/80 text-sm leading-relaxed">{mentor.bio}</p>
                      </div>
                    </div>

                    {/* Stats row */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <motion.div
                        className="text-center p-3 rounded-xl bg-white/5 border border-white/10"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 + 0.3 }}
                        viewport={{ once: true }}
                      >
                        <div className="text-lg font-bold text-primary">{mentor.companiesMentored}</div>
                        <div className="text-xs text-white/60">Companies</div>
                      </motion.div>
                      <motion.div
                        className="text-center p-3 rounded-xl bg-white/5 border border-white/10"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 + 0.4 }}
                        viewport={{ once: true }}
                      >
                        <div className="text-lg font-bold text-green-400">{mentor.successRate}</div>
                        <div className="text-xs text-white/60">Success Rate</div>
                      </motion.div>
                      <motion.div
                        className="text-center p-3 rounded-xl bg-white/5 border border-white/10"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 + 0.5 }}
                        viewport={{ once: true }}
                      >
                        <div className="text-lg font-bold text-blue-400">{mentor.experience}</div>
                        <div className="text-xs text-white/60">Experience</div>
                      </motion.div>
                    </div>

                    {/* Quote with enhanced styling */}
                    <motion.div
                      className="mb-6 p-4 rounded-xl bg-gradient-to-r from-white/5 to-white/10 border-l-4 border-primary/70"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 + 0.6 }}
                      viewport={{ once: true }}
                    >
                      <p className="text-white/90 italic text-sm leading-relaxed">"{mentor.quote}"</p>
                    </motion.div>

                    {/* Specialties with enhanced design */}
                    <div className="mb-6">
                      <h4 className="text-white/90 font-medium mb-3 text-sm">Areas of Expertise</h4>
                      <div className="flex flex-wrap gap-2">
                        {mentor.specialties.map((specialty, skillIndex) => (
                          <motion.span
                            key={specialty}
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ delay: index * 0.1 + skillIndex * 0.05 }}
                            className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium transition-all duration-300 border-primary/40 bg-primary/15 text-primary hover:bg-primary/25 hover:scale-105"
                          >
                            {specialty}
                          </motion.span>
                        ))}
                      </div>
                    </div>

                    {/* Enhanced footer */}
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-3">
                        {Object.entries(mentor.social).map(([platform, url]) => (
                          <motion.a
                            key={platform}
                            href={url}
                            className="w-9 h-9 rounded-xl bg-white/10 flex items-center justify-center text-white/60 hover:text-white hover:bg-primary/30 transition-all duration-300 border border-white/10 hover:border-primary/50"
                            whileHover={{ scale: 1.1, y: -2 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <span className="text-xs font-bold">{platform[0].toUpperCase()}</span>
                          </motion.a>
                        ))}
                      </div>

                      <motion.div
                        className="flex items-center space-x-2"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: index * 0.1 + 0.8 }}
                        viewport={{ once: true }}
                      >
                        <motion.div
                          className={`w-3 h-3 rounded-full bg-gradient-to-r ${mentor.gradient}`}
                          animate={{
                            scale: [1, 1.3, 1],
                            opacity: [0.7, 1, 0.7],
                          }}
                          transition={{
                            duration: 2.5,
                            repeat: Infinity,
                            delay: index * 0.7,
                            ease: "easeInOut"
                          }}
                        />
                        <span className="text-xs text-white/50">Available for mentorship</span>
                      </motion.div>
                    </div>

                    {/* Enhanced bottom accent */}
                    <motion.div
                      className={`absolute bottom-0 left-0 h-1.5 bg-gradient-to-r ${mentor.gradient} rounded-b-3xl w-0 group-hover:w-full transition-all duration-700`}
                      initial={false}
                    />

                    {/* Floating particles for premium feel */}
                    <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-3xl">
                      {[...Array(4)].map((_, particleIndex) => (
                        <motion.div
                          key={particleIndex}
                          className={`absolute w-1 h-1 rounded-full opacity-60`}
                          style={{
                            background: mentor.accentColor,
                            left: `${15 + particleIndex * 20}%`,
                            top: `${20 + particleIndex * 15}%`,
                          }}
                          animate={{
                            y: [-15, -25, -15],
                            opacity: [0, 1, 0],
                            scale: [0.5, 1, 0.5],
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            delay: particleIndex * 0.8 + index * 0.3,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </motion.div>
              </SmoothReveal>
            ))}
          </div>
        </div>
      </BokehBackground>

      {/* Final Call-to-Action Section */}
      <section className="py-32 bg-gradient-to-b from-black via-gray-900/50 to-black relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-primary/10" />
        <motion.div
          className="absolute inset-0 opacity-30"
          style={{ y: y1 }}
        >
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/20 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl" />
        </motion.div>

        <div className="container mx-auto px-4 relative">
          <SmoothReveal direction="up" className="text-center">
            <motion.div
              className="inline-block rounded-full bg-primary/10 px-6 py-3 text-sm font-medium text-primary mb-8 border border-primary/20"
              whileHover={{ scale: 1.05 }}
            >
              Ready to Start?
            </motion.div>
            <h2 className="text-4xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent max-w-4xl mx-auto leading-tight">
              Join the Next Generation of Successful Entrepreneurs
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed mb-12">
              Whether you're just starting out or looking to scale your existing business, InnoHub provides the resources, mentorship, and community you need to succeed.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <HoverBorderGradient
                containerClassName="rounded-full"
                className="bg-primary text-white px-10 py-4 text-lg font-medium"
              >
                <span className="flex items-center gap-2">
                  Apply to Our Programs
                  <ArrowRightIcon className="w-5 h-5" />
                </span>
              </HoverBorderGradient>

              <motion.button
                className="px-10 py-4 text-lg font-medium text-white/80 hover:text-white transition-colors duration-300 border border-white/20 rounded-full hover:border-white/40"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Schedule a Consultation
              </motion.button>
            </div>
          </SmoothReveal>
        </div>
      </section>
    </div>
  );
}
